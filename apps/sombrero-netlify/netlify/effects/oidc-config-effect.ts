import type { z } from "zod";

import { Cache, Duration, Effect } from "effect";

import {
	type Jwk,
	type Jwks,
	JwksSchema,
	OidcConfigSchema,
} from "../schemas/oidc-schema";

/** Error types for OIDC configuration operations */
export type OidcConfigError =
	| "INVALID_CIAM_LOGIN_URL_FORMAT"
	| "JWK_NOT_FOUND"
	| "JWKS_FETCH_FAILED"
	| "MISSING_CIAM_LOGIN_URL"
	| "NO_KEYS_AVAILABLE"
	| "OIDC_CONFIG_FETCH_FAILED";

/** Configuration for OIDC caching */
export type OidcCacheConfig = {
	capacity: number;
	timeToLiveHours: number;
};

/** Default cache configuration */
const DEFAULT_CACHE_CONFIG: OidcCacheConfig = {
	capacity: 100,
	timeToLiveHours: 1,
};

/** Common HTTP headers for OIDC requests */
const OIDC_HTTP_HEADERS = {
	Accept: "application/json",
	"User-Agent": "ANWB-Sombrero-Netlify/1.0",
} as const;

/**
 * Creates a generic HTTP effect for fetching and validating JSON data
 *
 * @param url - The URL to fetch from
 * @param schema - Zod schema for validation
 * @param errorType - Error type to return on failure
 */
const createHttpEffect = <T>(
	url: string,
	schema: z.ZodSchema<T>,
	errorType: OidcConfigError,
) =>
	Effect.tryPromise({
		catch: (error) => {
			console.error(`HTTP fetch failed for ${url}:`, error);
			return errorType;
		},
		try: async (): Promise<T> => {
			const response = await fetch(url, {
				headers: OIDC_HTTP_HEADERS,
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status} ${response.statusText}`);
			}

			const data = await response.json();
			return schema.parse(data);
		},
	});

/**
 * Parses the issuer URL from the CIAM login URL Expected format:
 * https://login-{env}.anwb.nl/{tenant-id}/login
 */
const parseIssuerUrlFromLoginUrl = (loginUrl: string) =>
	Effect.try({
		catch: () => "INVALID_CIAM_LOGIN_URL_FORMAT" as const,
		try: (): string => {
			// More specific regex for ANWB login URL format
			const urlPattern =
				/^https:\/\/(login-[^.]+\.anwb\.nl)\/([^/]+)\/login\/?$/;
			const urlMatch = urlPattern.exec(loginUrl.trim());

			if (!urlMatch) {
				throw new Error(`Invalid CIAM_LOGIN_API_URL format: ${loginUrl}`);
			}

			const [, host, tenantId] = urlMatch;

			if (!host || !tenantId) {
				throw new Error("Missing host or tenant ID in URL");
			}

			return `https://${host}/${tenantId}/login`;
		},
	});

/** Determines the expected issuer URL based on the current environment */
export const getExpectedIssuerEffect = Effect.try({
	catch: () => "MISSING_CIAM_LOGIN_URL" as const,
	try: () => {
		const loginUrl = process.env.CIAM_LOGIN_API_URL;

		if (!loginUrl) {
			throw new Error("CIAM_LOGIN_API_URL environment variable is not set");
		}

		return loginUrl;
	},
}).pipe(Effect.flatMap(parseIssuerUrlFromLoginUrl));

/**
 * Fetches OIDC configuration from the well-known endpoint
 *
 * @param issuerUrl - The issuer URL to fetch configuration for
 */
const fetchOidcConfigEffect = (issuerUrl: string) => {
	const configUrl = `${issuerUrl}/.well-known/openid-configuration`;
	return createHttpEffect(
		configUrl,
		OidcConfigSchema,
		"OIDC_CONFIG_FETCH_FAILED",
	);
};

/**
 * Fetches JWK public keys from the JWKS endpoint
 *
 * @param jwksUri - The JWKS URI to fetch keys from
 */
const fetchJwksEffect = (jwksUri: string) =>
	createHttpEffect(jwksUri, JwksSchema, "JWKS_FETCH_FAILED");

/**
 * Creates OIDC configuration cache with configurable settings
 *
 * @param config - Cache configuration options
 */
const createOidcConfigCache = (
	config: OidcCacheConfig = DEFAULT_CACHE_CONFIG,
) =>
	Cache.make({
		capacity: config.capacity,
		lookup: (issuerUrl: string) => fetchOidcConfigEffect(issuerUrl),
		timeToLive: Duration.hours(config.timeToLiveHours),
	});

/**
 * Creates JWKS cache with configurable settings
 *
 * @param config - Cache configuration options
 */
const createJwksCache = (config: OidcCacheConfig = DEFAULT_CACHE_CONFIG) =>
	Cache.make({
		capacity: config.capacity,
		lookup: (jwksUri: string) => fetchJwksEffect(jwksUri),
		timeToLive: Duration.hours(config.timeToLiveHours),
	});

/** Default OIDC configuration cache */
const oidcConfigCacheEffect = createOidcConfigCache();

/** Default JWKS cache */
const jwksCacheEffect = createJwksCache();

/**
 * Cached OIDC configuration effect
 *
 * @param issuerUrl - The issuer URL to get configuration for
 */
export const oidcConfigEffect = (issuerUrl: string) =>
	oidcConfigCacheEffect.pipe(Effect.flatMap((cache) => cache.get(issuerUrl)));

/**
 * Cached JWKS effect
 *
 * @param jwksUri - The JWKS URI to get keys from
 */
export const jwksEffect = (jwksUri: string) =>
	jwksCacheEffect.pipe(Effect.flatMap((cache) => cache.get(jwksUri)));

/**
 * Combined effect to get OIDC config and JWKS
 *
 * @param issuerUrl - The issuer URL to get configuration and keys for
 */
export const oidcConfigAndJwksEffect = (issuerUrl: string) =>
	oidcConfigEffect(issuerUrl).pipe(
		Effect.flatMap((config) =>
			jwksEffect(config.jwks_uri).pipe(
				Effect.map((jwks) => ({ config, jwks })),
			),
		),
	);

/**
 * Finds a JWK by key ID (kid) from the JWKS (synchronous operation)
 *
 * @param jwks - The JWKS containing the keys
 * @param kid - Optional key ID to search for. If not provided, returns the
 *   first key
 */
export const findJwkByKidEffect = (jwks: Jwks, kid?: string) =>
	Effect.try({
		catch: (): OidcConfigError => {
			if (jwks.keys.length === 0) {
				return "NO_KEYS_AVAILABLE";
			}
			return "JWK_NOT_FOUND";
		},
		try: (): Jwk => {
			if (jwks.keys.length === 0) {
				throw new Error("No keys available in JWKS");
			}

			if (!kid) {
				// If no kid specified, return the first key
				const firstKey = jwks.keys[0];
				if (!firstKey) {
					throw new Error("No keys available in JWKS");
				}
				return firstKey;
			}

			const key = jwks.keys.find((k) => k.kid === kid);
			if (!key) {
				throw new Error(`No key found with kid: ${kid}`);
			}
			return key;
		},
	});

/**
 * Complete effect to get expected issuer, OIDC config, and JWKS This is the
 * main entry point that combines all OIDC operations
 */
export const completeOidcEffect = getExpectedIssuerEffect.pipe(
	Effect.flatMap((issuerUrl) =>
		oidcConfigAndJwksEffect(issuerUrl).pipe(
			Effect.map(({ config, jwks }) => ({
				config,
				issuerUrl,
				jwks,
			})),
		),
	),
);

/**
 * Effect to find a specific JWK by kid using the complete OIDC flow
 *
 * @param kid - Optional key ID to search for. If not provided, returns the
 *   first key
 */
export const findJwkEffect = (kid?: string) =>
	completeOidcEffect.pipe(
		Effect.flatMap(({ jwks }) => findJwkByKidEffect(jwks, kid)),
	);

/**
 * Creates a configurable OIDC service with custom cache settings This allows
 * for dependency injection and better testability
 *
 * @param config - Optional cache configuration
 * @returns Object with all OIDC operations using the provided configuration
 */
export const createOidcService = (config?: Partial<OidcCacheConfig>) => {
	const fullConfig: OidcCacheConfig = { ...DEFAULT_CACHE_CONFIG, ...config };
	const oidcCache = createOidcConfigCache(fullConfig);
	const jwksCache = createJwksCache(fullConfig);

	const getOidcConfig = (issuerUrl: string) =>
		oidcCache.pipe(Effect.flatMap((cache) => cache.get(issuerUrl)));

	const getJwks = (jwksUri: string) =>
		jwksCache.pipe(Effect.flatMap((cache) => cache.get(jwksUri)));

	const getOidcConfigAndJwks = (issuerUrl: string) =>
		getOidcConfig(issuerUrl).pipe(
			Effect.flatMap((config) =>
				getJwks(config.jwks_uri).pipe(Effect.map((jwks) => ({ config, jwks }))),
			),
		);

	const getCompleteOidc = getExpectedIssuerEffect.pipe(
		Effect.flatMap((issuerUrl) =>
			getOidcConfigAndJwks(issuerUrl).pipe(
				Effect.map(({ config, jwks }) => ({
					config,
					issuerUrl,
					jwks,
				})),
			),
		),
	);

	const findJwk = (kid?: string) =>
		getCompleteOidc.pipe(
			Effect.flatMap(({ jwks }) => findJwkByKidEffect(jwks, kid)),
		);

	return {
		findJwk,
		findJwkByKid: findJwkByKidEffect,
		getCompleteOidc,
		getJwks,
		getOidcConfig,
		getOidcConfigAndJwks,
	};
};
