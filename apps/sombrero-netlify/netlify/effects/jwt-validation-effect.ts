import type { J<PERSON><PERSON>, J<PERSON><PERSON>ayload } from "jose";

import { Effect } from "effect";
import { decodeProtected<PERSON><PERSON><PERSON>, importJ<PERSON><PERSON>, jwtVerify } from "jose";

import {
	JwtClaimsSchema,
	type JwtValidationError,
} from "../schemas/jwt-claims-schema";
import { completeOidcEffect, findJwkByKidEffect } from "./oidc-config-effect";

/** Map a JWT error message to a JwtValidationError from our schema */
const mapJwtError = (error: unknown): JwtValidationError => {
	if (error instanceof Error) {
		const message = error.message.toLowerCase();
		if (message.includes("expired")) return "JWT_EXPIRED";
		if (message.includes("issuer")) return "JWT_ISSUER_MISMATCH";
		if (message.includes("signature")) return "JWT_SIGNATURE_INVALID";
		if (message.includes("not yet valid")) return "JWT_NOT_YET_VALID";
		if (message.includes("claims")) return "JWT_CLAIMS_INVALID";
	}
	return "JWT_PARSE_ERROR";
};

/**
 * Main JWT validation Effect that uses cached OIDC configuration
 *
 * This is the main validation function that performs comprehensive JWT
 * validation:
 *
 * 1. JWT parsing and structure validation
 * 2. Signature verification using OIDC public keys (cached)
 * 3. Claims validation (structure only - timing/issuer handled by jose)
 *
 * This Effect never fails - all errors are converted to validation results.
 *
 * @param accessToken - The JWT access token to validate
 * @returns Effect that resolves to validation result
 */
export const validateAccessTokenJwtEffect = (accessToken: string) =>
	Effect.Do.pipe(
		// Parse JWT header to extract key ID
		Effect.bind("kid", () =>
			Effect.tryPromise({
				catch: () => "JWT_PARSE_ERROR" as const,
				try: async (): Promise<string | undefined> => {
					const header = decodeProtectedHeader(accessToken);
					return header.kid;
				},
			}),
		),

		// Get OIDC configuration and JWKS
		Effect.bind("oidcData", () => completeOidcEffect),

		// Find the matching JWK by key ID
		Effect.bind("jwk", ({ kid, oidcData }) =>
			findJwkByKidEffect(oidcData.jwks, kid),
		),

		// Convert JWK to CryptoKey object for jose
		Effect.bind("publicKey", ({ jwk }) =>
			Effect.tryPromise({
				catch: () => "JWT_SIGNATURE_INVALID" as const,
				try: (): Promise<CryptoKey | Uint8Array> => importJWK(jwk as JWK),
			}),
		),

		// Verify JWT signature and extract payload (includes timing and issuer validation)
		Effect.bind("payload", ({ oidcData, publicKey }) =>
			Effect.tryPromise({
				catch: (error) => {
					console.error("JWT verification failed:", error);
					return mapJwtError(error);
				},
				try: async (): Promise<JWTPayload> => {
					const { payload } = await jwtVerify(accessToken, publicKey, {
						issuer: oidcData.issuerUrl,
						// Note: We don't validate audience here as it may vary
						// clockTolerance: 30, // Allow 30 seconds clock skew
					});
					return payload;
				},
			}),
		),

		// Validate JWT claims structure
		Effect.bind("claims", ({ payload }) =>
			Effect.tryPromise({
				catch: (error) => {
					console.error("JWT claims validation failed:", error);
					return "JWT_CLAIMS_INVALID" as const;
				},
				try: async () => JwtClaimsSchema.parse(payload),
			}),
		),

		Effect.map(
			({ claims }) =>
				({
					claims,
					success: true,
				}) as const,
		),

		Effect.catchAll((error) =>
			Effect.succeed({
				error: typeof error === "string" ? error : "JWT_PARSE_ERROR",
				success: false,
			} as const),
		),
	);
